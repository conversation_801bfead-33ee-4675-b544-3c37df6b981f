#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
专门测试奇安信浏览器"首页"按钮的识别问题
"""

import os
import sys
import time
import pyatspi

# 设置DISPLAY环境变量
os.environ['DISPLAY'] = ':0'

# 添加scripts目录到Python路径
sys.path.insert(0, '/home/<USER>/kylin-robot-ide/scripts')

try:
    from UNI import UNI
    print("成功导入UNI模块")
except ImportError as e:
    print(f"导入UNI模块失败: {e}")
    sys.exit(1)

def analyze_homepage_button():
    """分析首页按钮的详细信息"""
    print("=" * 60)
    print("分析奇安信浏览器首页按钮")
    print("=" * 60)
    
    test_x, test_y = 190, 47
    print(f"测试坐标: ({test_x}, {test_y})")
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        # 找到浏览器应用
        browser_app = None
        for i in range(desktop.childCount):
            app = desktop.getChildAtIndex(i)
            if "浏览器" in app.name:
                browser_app = app
                break
        
        if not browser_app:
            print("未找到浏览器应用")
            return
        
        print(f"找到浏览器应用: {browser_app.name}")
        
        # 找到浏览器窗口
        browser_window = None
        for j in range(browser_app.childCount):
            window = browser_app.getChildAtIndex(j)
            extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
            if (extents.x <= test_x < extents.x + extents.width and
                extents.y <= test_y < extents.y + extents.height):
                browser_window = window
                break
        
        if not browser_window:
            print("未找到包含测试坐标的浏览器窗口")
            return
        
        print(f"找到浏览器窗口: {browser_window.name}")
        
        # 深度搜索首页按钮
        def find_controls_at_point(element, x, y, depth=0, max_depth=10):
            """深度搜索指定坐标的所有控件"""
            results = []
            
            if depth > max_depth:
                return results
            
            try:
                if hasattr(element, 'queryComponent'):
                    extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    contains_point = (extents.x <= x < extents.x + extents.width and
                                    extents.y <= y < extents.y + extents.height)
                    
                    if contains_point:
                        # 获取详细信息
                        name = element.name if element.name else "unnamed"
                        role = element.getRoleName() if hasattr(element, 'getRoleName') else "unknown"
                        
                        # 获取状态
                        try:
                            states = element.getState().getStates()
                            state_names = [pyatspi.stateToString(state) for state in states]
                        except:
                            state_names = []
                        
                        # 获取描述
                        try:
                            description = element.description if hasattr(element, 'description') else ""
                        except:
                            description = ""
                        
                        # 获取文本内容
                        try:
                            if hasattr(element, 'queryText'):
                                text_interface = element.queryText()
                                text_content = text_interface.getText(0, -1)
                            else:
                                text_content = ""
                        except:
                            text_content = ""
                        
                        # 获取动作
                        try:
                            if hasattr(element, 'queryAction'):
                                action_interface = element.queryAction()
                                actions = []
                                for i in range(action_interface.nActions):
                                    action_name = action_interface.getName(i)
                                    actions.append(action_name)
                            else:
                                actions = []
                        except:
                            actions = []
                        
                        area = extents.width * extents.height
                        
                        control_info = {
                            'element': element,
                            'name': name,
                            'role': role,
                            'description': description,
                            'text_content': text_content,
                            'actions': actions,
                            'area': area,
                            'depth': depth,
                            'coords': (extents.x, extents.y, extents.width, extents.height),
                            'states': state_names,
                            'child_count': element.childCount if hasattr(element, 'childCount') else 0
                        }
                        
                        results.append(control_info)
                        
                        # 继续搜索子控件
                        if hasattr(element, 'childCount'):
                            for i in range(element.childCount):
                                try:
                                    child = element.getChildAtIndex(i)
                                    if child:
                                        child_results = find_controls_at_point(child, x, y, depth + 1, max_depth)
                                        results.extend(child_results)
                                except:
                                    continue
                                    
            except Exception as e:
                print(f"处理控件时出错 (深度{depth}): {e}")
                
            return results
        
        print(f"\n开始深度搜索坐标({test_x}, {test_y})处的控件...")
        controls = find_controls_at_point(browser_window, test_x, test_y)
        
        print(f"找到 {len(controls)} 个控件:")
        print("-" * 80)
        
        for i, ctrl in enumerate(controls):
            print(f"\n[{i}] 控件信息:")
            print(f"  名称: {ctrl['name']}")
            print(f"  角色: {ctrl['role']}")
            print(f"  描述: {ctrl['description']}")
            print(f"  文本内容: '{ctrl['text_content']}'")
            print(f"  动作: {ctrl['actions']}")
            print(f"  深度: {ctrl['depth']}")
            print(f"  面积: {ctrl['area']}")
            print(f"  坐标: {ctrl['coords']}")
            print(f"  状态: {ctrl['states']}")
            print(f"  子控件数: {ctrl['child_count']}")
        
        # 找到最可能是首页按钮的控件
        print(f"\n" + "=" * 60)
        print("分析哪个控件最可能是首页按钮:")
        print("=" * 60)
        
        # 按面积排序，找最小的
        controls.sort(key=lambda x: x['area'])
        
        for i, ctrl in enumerate(controls[:3]):  # 显示前3个最小的
            print(f"\n候选 {i+1} (面积: {ctrl['area']}):")
            print(f"  名称: {ctrl['name']}")
            print(f"  角色: {ctrl['role']}")
            print(f"  文本: '{ctrl['text_content']}'")
            print(f"  动作: {ctrl['actions']}")
            print(f"  坐标: {ctrl['coords']}")
            
            # 判断是否可能是按钮
            is_button_like = (
                'button' in ctrl['role'].lower() or
                'click' in ' '.join(ctrl['actions']).lower() or
                'press' in ' '.join(ctrl['actions']).lower() or
                'activate' in ' '.join(ctrl['actions']).lower()
            )
            
            has_text_content = bool(ctrl['text_content'].strip())
            has_meaningful_name = ctrl['name'] != 'unnamed' and bool(ctrl['name'].strip())
            
            print(f"  按钮特征: {is_button_like}")
            print(f"  有文本内容: {has_text_content}")
            print(f"  有意义名称: {has_meaningful_name}")
            
            if is_button_like or has_text_content or has_meaningful_name:
                print(f"  *** 这可能是首页按钮 ***")
        
    except Exception as e:
        print(f"分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_uni_recognition():
    """测试UNI模块的识别结果"""
    print(f"\n" + "=" * 60)
    print("测试UNI模块识别结果:")
    print("=" * 60)
    
    uni = UNI()
    test_x, test_y = 190, 47
    
    try:
        data, message = uni.kdk_getElement_Uni(test_x, test_y)
        print(f"UNI识别结果: {message}")
        
        if data and 'error' not in data:
            print(f"名称: {data.get('Name', 'N/A')}")
            print(f"角色: {data.get('Rolename', 'N/A')}")
            print(f"描述: {data.get('Description', 'N/A')}")
            print(f"文本: {data.get('Text', 'N/A')}")
            print(f"坐标: {data.get('Coords', 'N/A')}")
            
            # 建议改进
            print(f"\n改进建议:")
            if data.get('Name') == 'N/A' or not data.get('Name'):
                print("- 控件缺少名称信息，可能需要OCR识别")
            if data.get('Rolename') == 'filler':
                print("- 控件角色为filler，可能是容器而非按钮")
            if not data.get('Text'):
                print("- 控件缺少文本内容")
        else:
            print("UNI识别失败或返回错误")
            
    except Exception as e:
        print(f"UNI测试出错: {e}")

if __name__ == "__main__":
    analyze_homepage_button()
    test_uni_recognition()
