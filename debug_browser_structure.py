#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
调试奇安信浏览器窗口内部结构
"""

import os
import sys
import time
import pyatspi

# 设置DISPLAY环境变量
os.environ['DISPLAY'] = ':0'

# 添加scripts目录到Python路径
sys.path.insert(0, '/home/<USER>/kylin-robot-ide/scripts')

try:
    from UNI import UNI
    print("成功导入UNI模块")
except ImportError as e:
    print(f"导入UNI模块失败: {e}")
    sys.exit(1)

def print_element_tree(element, depth=0, max_depth=5):
    """递归打印控件树结构"""
    if depth > max_depth:
        return
    
    indent = "  " * depth
    try:
        name = element.name if element.name else "unnamed"
        role = element.getRoleName() if hasattr(element, 'getRoleName') else "unknown"
        
        # 获取坐标信息
        try:
            extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
            coords = f"({extents.x}, {extents.y}, {extents.width}, {extents.height})"
        except:
            coords = "(无坐标信息)"
        
        # 获取状态信息
        try:
            states = element.getState().getStates()
            state_names = [pyatspi.stateToString(state) for state in states]
            visible = 'showing' in state_names or 'visible' in state_names
            enabled = 'enabled' in state_names
            status = f"可见:{visible} 启用:{enabled}"
        except:
            status = "状态未知"
        
        print(f"{indent}[{depth}] {name} ({role}) {coords} {status}")
        
        # 递归打印子控件
        if hasattr(element, 'childCount'):
            for i in range(element.childCount):
                try:
                    child = element.getChildAtIndex(i)
                    if child:
                        print_element_tree(child, depth + 1, max_depth)
                except Exception as e:
                    print(f"{indent}  [ERROR] 获取子控件 {i} 失败: {e}")
                    
    except Exception as e:
        print(f"{indent}[ERROR] 处理控件时出错: {e}")

def find_browser_window():
    """查找奇安信浏览器窗口"""
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        print(f"桌面应用程序数量: {desktop.childCount}")
        
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                app_name = app.name if app.name else "unnamed"
                print(f"\n应用程序 [{i}]: {app_name}")
                
                # 查找奇安信浏览器
                if "浏览器" in app_name or "qax" in app_name.lower() or "browser" in app_name.lower():
                    print(f"  找到浏览器应用: {app_name}")
                    print(f"  窗口数量: {app.childCount}")
                    
                    for j in range(app.childCount):
                        try:
                            window = app.getChildAtIndex(j)
                            window_name = window.name if window.name else "unnamed"
                            window_role = window.getRoleName() if hasattr(window, 'getRoleName') else "unknown"
                            
                            print(f"    窗口 [{j}]: {window_name} ({window_role})")
                            
                            # 检查窗口是否包含测试坐标
                            try:
                                extents = window.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                                test_x, test_y = 190, 47
                                contains_point = (extents.x <= test_x < extents.x + extents.width and
                                                extents.y <= test_y < extents.y + extents.height)
                                print(f"      坐标: ({extents.x}, {extents.y}, {extents.width}, {extents.height})")
                                print(f"      包含测试点({test_x}, {test_y}): {contains_point}")
                                
                                if contains_point:
                                    print(f"      *** 这是目标窗口，开始分析内部结构 ***")
                                    print(f"      子控件数量: {window.childCount}")
                                    print_element_tree(window, 0, 3)
                                    return window
                                    
                            except Exception as e:
                                print(f"      获取窗口坐标失败: {e}")
                                
                        except Exception as e:
                            print(f"    获取窗口 {j} 失败: {e}")
                            
            except Exception as e:
                print(f"获取应用程序 {i} 失败: {e}")
                
    except Exception as e:
        print(f"查找浏览器窗口时出错: {e}")
        
    return None

def test_coordinate_search():
    """测试坐标搜索"""
    print("\n" + "=" * 60)
    print("测试坐标搜索功能")
    print("=" * 60)
    
    test_x, test_y = 190, 47
    
    try:
        desktop = pyatspi.Registry.getDesktop(0)
        
        def search_at_coordinate(element, x, y, depth=0, max_depth=10):
            """在指定坐标搜索控件"""
            if depth > max_depth:
                return []
            
            results = []
            try:
                if hasattr(element, 'queryComponent'):
                    extents = element.queryComponent().getExtents(pyatspi.DESKTOP_COORDS)
                    contains_point = (extents.x <= x < extents.x + extents.width and
                                    extents.y <= y < extents.y + extents.height)
                    
                    if contains_point:
                        name = element.name if element.name else "unnamed"
                        role = element.getRoleName() if hasattr(element, 'getRoleName') else "unknown"
                        area = extents.width * extents.height
                        
                        results.append({
                            'element': element,
                            'name': name,
                            'role': role,
                            'area': area,
                            'depth': depth,
                            'coords': (extents.x, extents.y, extents.width, extents.height)
                        })
                        
                        # 继续搜索子控件
                        for i in range(element.childCount):
                            try:
                                child = element.getChildAtIndex(i)
                                if child:
                                    child_results = search_at_coordinate(child, x, y, depth + 1, max_depth)
                                    results.extend(child_results)
                            except:
                                continue
                                
            except Exception as e:
                pass
                
            return results
        
        print(f"搜索坐标 ({test_x}, {test_y}) 处的所有控件...")
        all_results = []
        
        for i in range(desktop.childCount):
            try:
                app = desktop.getChildAtIndex(i)
                for j in range(app.childCount):
                    try:
                        window = app.getChildAtIndex(j)
                        results = search_at_coordinate(window, test_x, test_y)
                        all_results.extend(results)
                    except:
                        continue
            except:
                continue
        
        print(f"找到 {len(all_results)} 个包含该坐标的控件:")
        for i, result in enumerate(all_results):
            print(f"  [{i}] {result['name']} ({result['role']}) "
                  f"深度:{result['depth']} 面积:{result['area']} "
                  f"坐标:{result['coords']}")
        
        # 按面积排序，找到最小的控件
        if all_results:
            all_results.sort(key=lambda x: x['area'])
            smallest = all_results[0]
            print(f"\n最小控件: {smallest['name']} ({smallest['role']}) 面积:{smallest['area']}")
            
    except Exception as e:
        print(f"坐标搜索时出错: {e}")

if __name__ == "__main__":
    print("开始分析奇安信浏览器窗口结构...")
    browser_window = find_browser_window()
    
    if browser_window:
        print(f"\n找到浏览器窗口: {browser_window.name}")
    else:
        print("\n未找到浏览器窗口")
    
    test_coordinate_search()
