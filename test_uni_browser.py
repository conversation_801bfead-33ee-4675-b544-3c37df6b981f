#!/usr/bin/env python3
# -*- coding=utf-8 -*-
"""
测试UNI.py模块在奇安信浏览器中的控件识别功能
"""

import os
import sys
import time

# 设置DISPLAY环境变量
os.environ['DISPLAY'] = ':0'

# 添加scripts目录到Python路径
sys.path.insert(0, '/home/<USER>/kylin-robot-ide/scripts')

try:
    from UNI import UNI
    print("成功导入UNI模块")
except ImportError as e:
    print(f"导入UNI模块失败: {e}")
    sys.exit(1)

def test_browser_control_recognition():
    """测试浏览器控件识别"""
    print("=" * 60)
    print("测试奇安信浏览器控件识别")
    print("=" * 60)
    
    # 创建UNI实例
    uni = UNI()
    
    # 测试坐标
    test_x, test_y = 190, 47
    
    print(f"测试坐标: ({test_x}, {test_y})")
    print(f"显示服务器类型: {uni.display_server}")
    print(f"X11可用: {uni.x11_available}")
    print(f"WNCK可用: {uni.wnck_available}")
    print(f"DISPLAY环境变量: {os.environ.get('DISPLAY', 'Not set')}")
    
    print("\n开始控件识别...")
    
    try:
        # 调用控件识别方法
        start_time = time.time()
        data, message = uni.kdk_getElement_Uni(test_x, test_y)
        end_time = time.time()
        
        print(f"识别耗时: {end_time - start_time:.3f}秒")
        print(f"返回消息: {message}")
        
        if data:
            print("\n识别结果:")
            print("-" * 40)
            
            # 打印关键信息
            key_fields = ['Name', 'Rolename', 'ProcessName', 'WindowName', 'Description', 'capture_status']
            for field in key_fields:
                if field in data:
                    print(f"{field}: {data[field]}")
            
            # 打印坐标信息
            if 'Coords' in data:
                coords = data['Coords']
                print(f"Coords: x={coords.get('x', 'N/A')}, y={coords.get('y', 'N/A')}, "
                      f"width={coords.get('width', 'N/A')}, height={coords.get('height', 'N/A')}")
            
            # 打印错误信息（如果有）
            if 'error' in data:
                print(f"错误信息: {data['error']}")
            
            print("\n完整数据:")
            print("-" * 40)
            for key, value in data.items():
                print(f"{key}: {value}")
        else:
            print("未获取到控件数据")
            
    except Exception as e:
        print(f"控件识别过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

def test_window_detection():
    """测试窗口检测功能"""
    print("\n" + "=" * 60)
    print("测试窗口检测功能")
    print("=" * 60)
    
    uni = UNI()
    test_x, test_y = 190, 47
    
    try:
        # 测试活动窗口获取
        print("获取活动窗口...")
        active_window, processid, activewindow_region, windowRoleName, windowChildCount = uni._get_active_window2(test_x, test_y)
        
        if active_window:
            print(f"活动窗口名称: {getattr(active_window, 'name', 'N/A')}")
            print(f"进程ID: {processid}")
            print(f"窗口区域: {activewindow_region}")
            print(f"窗口角色: {windowRoleName}")
            print(f"子控件数量: {windowChildCount}")
        else:
            print("未找到活动窗口")
            
        # 测试最上层窗口获取
        print("\n获取最上层窗口...")
        topmost_window = uni.print_topmost_window_info(test_x, test_y)
        
        if topmost_window:
            if hasattr(topmost_window, 'name'):
                print(f"最上层窗口名称: {topmost_window.name}")
            if hasattr(topmost_window, 'get_name'):
                print(f"最上层窗口名称: {topmost_window.get_name()}")
            print(f"最上层窗口类型: {type(topmost_window)}")
        else:
            print("未找到最上层窗口")
            
    except Exception as e:
        print(f"窗口检测过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_window_detection()
    test_browser_control_recognition()
